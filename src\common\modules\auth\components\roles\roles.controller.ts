import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { UpdateRoleDto } from './dto/update-role.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RequirePermission } from '../common/decorators/require-permission.decorator';
import { PermissionGuard } from 'src/common/modules/auth/guards/permission.guard';
import { NotRequireLogin } from '../common/decorators/not-require-login.decorator';
import { CreateRoleDto, SearchRoleDto } from './dto/create-role.dto';
import { MenusService } from '../menus/menus.service';

@ApiTags('roles')
@Controller('roles')
@ApiBearerAuth() // 开启 BearerAuth 授权认证
// @UseGuards(PermissionGuard) // 使用权限守卫
export class RolesController {
  constructor(
    private readonly rolesService: RolesService,
    private readonly menusService: MenusService,
  ) {}

  // @RequirePermission(['新增 aaa'])
  @Post('/create')
  @ApiOperation({ summary: '创建角色' })
  @ApiResponse({ status: 201, description: '创建成功' })
  @ApiResponse({ status: 403, description: '没有权限' })
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.create(createRoleDto);
  }

  @Patch('/update/:id')
  @ApiOperation({ summary: '更新角色' })
  @ApiResponse({ status: 201, description: '更新成功' })
  @ApiResponse({ status: 403, description: '没有权限' })
  update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.rolesService.update(+id, updateRoleDto);
  }

  @Get('/all')
  @ApiOperation({ summary: '查询所有角色' })
  async findAll() {
    return await this.rolesService.findAll();
  }

  @Post('/role-menu')
  @ApiOperation({ summary: '获取所有菜单用于角色分配' })
  @ApiResponse({ status: 200, description: '返回所有菜单信息' })
  async GetRoleMenu() {
    const menus = await this.menusService.findAll();

    return menus.map((menu) => {
      return {
        parentId: menu.parent?.id ?? 0,
        id: menu.id,
        menuType: menu.menuType,
        title: menu.title,
      };
    });
  }

  @Post('/role-menu-ids')
  @ApiOperation({ summary: '根据角色ID获取关联的菜单ID列表' })
  @ApiResponse({ status: 200, description: '返回菜单ID列表' })
  @ApiResponse({ status: 404, description: '角色不存在' })
  async GetRoleMenuIds(@Body() body: { id: number }) {
    // 根据角色id查询角色的菜单
    const roles = await this.rolesService.findRolesByIds([body.id]);

    // 返回角色的菜单id
    if (roles.length === 0) {
      throw new NotFoundException('角色不存在');
    } else {
      return roles[0].menus.map((menu) => menu.id);
    }
  }

  // 更新角色菜单权限
  @Post('/update-role-menu/:id')
  @ApiOperation({ summary: '更新角色菜单权限' })
  async updateRoleMenu(
    @Param('id') id: string,
    @Body() data: { menuIds: number[] },
  ) {
    return await this.rolesService.updateRoleMenu(+id, data.menuIds);
  }

  @Post('/search')
  @ApiOperation({ summary: '根据条件查询用户' })
  async findByCondition(@Body() searchRoleDto: SearchRoleDto) {
    // 判断是否为空或者空对象
    return await this.rolesService.findByCondition(searchRoleDto);
  }

  @Delete('/delete')
  @ApiOperation({ summary: '批量删除角色' })
  async delete(@Body() data: { ids: number[] }) {
    return await this.rolesService.delete(data.ids);
  }

  @Post('/ids')
  @ApiOperation({ summary: '根据用户id列表查询角色列表' })
  findListByIds(@Body() body: { roleIds: number[] }) {
    return this.rolesService.findRolesByIds(body.roleIds);
  }

  @Get(':id')
  @ApiOperation({ summary: '查询角色' })
  findOne(@Param('id') id: string) {
    return this.rolesService.findOne(+id);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除角色' })
  remove(@Param('id') id: string) {
    return this.rolesService.remove(+id);
  }
}
