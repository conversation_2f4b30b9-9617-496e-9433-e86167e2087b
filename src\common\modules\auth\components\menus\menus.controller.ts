import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  NotFoundException,
} from '@nestjs/common';
import { MenusService } from './menus.service';
import { CreateMenuDto, SearchMenuDto } from './dto/create-menu.dto';
import { UpdateMenuDto } from './dto/update-menu.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

@ApiTags('menus')
@Controller('menus')
@ApiBearerAuth() // 开启 BearerAuth 授权认证
export class MenusController {
  constructor(private readonly menusService: MenusService) {}

  @Post()
  @ApiOperation({ summary: '创建菜单' })
  @ApiResponse({ status: 201, description: '菜单创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  create(@Body() createMenuDto: CreateMenuDto) {
    return this.menusService.create(createMenuDto);
  }

  @Post('/search')
  @ApiOperation({ summary: '根据条件查询菜单' })
  @ApiResponse({ status: 200, description: '返回符合条件的菜单列表' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async findByCondition(@Body() searchMenuDto: SearchMenuDto) {
    // 判断是否为空或者空对象
    return await this.menusService.findByCondition(searchMenuDto);
  }

  @Post('/create')
  @ApiOperation({ summary: '新增菜单' })
  @ApiResponse({ status: 201, description: '菜单新增成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  createMenu(@Body() createMenuDto: CreateMenuDto) {
    return this.menusService.create(createMenuDto);
  }

  @Patch('/update/:id')
  @ApiOperation({ summary: '修改菜单' })
  @ApiResponse({ status: 200, description: '菜单修改成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '菜单不存在' })
  updateMenu(@Param('id') id: string, @Body() updateMenuDto: UpdateMenuDto) {
    return this.menusService.update(+id, updateMenuDto);
  }

  @Delete('/delete')
  @ApiOperation({ summary: '批量删除菜单' })
  @ApiResponse({ status: 200, description: '菜单批量删除成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '部分菜单不存在' })
  async delete(@Body() data: { ids: number[] }) {
    return await this.menusService.delete(data.ids);
  }

  @Get('/get-async-routes')
  @ApiOperation({ summary: '异步获取菜单路由' })
  @ApiResponse({ status: 200, description: '返回菜单路由信息' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getAsyncRoutes() {
    return await this.menusService.getAsyncRoutes();
  }

  @Get()
  @ApiOperation({ summary: '获取所有菜单' })
  @ApiResponse({ status: 200, description: '返回所有菜单列表' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async findAll() {
    const menus = await this.menusService.findAll();
    return menus; // 直接返回数据，拦截器会自动包装成 { success: true, data: menus }
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取菜单详情' })
  @ApiResponse({ status: 200, description: '返回菜单详情' })
  @ApiResponse({ status: 404, description: '菜单不存在' })
  async findOne(@Param('id') id: string) {
    const menu = await this.menusService.findOne(+id);
    if (!menu) {
      throw new NotFoundException('查询不到指定 id 的 menu'); // 抛出异常，过滤器会自动处理
    }
    return menu;
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除指定ID的菜单' })
  @ApiResponse({ status: 200, description: '菜单删除成功' })
  @ApiResponse({ status: 404, description: '菜单不存在' })
  remove(@Param('id') id: string) {
    return this.menusService.remove(+id);
  }
}
