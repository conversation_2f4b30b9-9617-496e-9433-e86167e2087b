import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CreateMenuDto {
  @ApiProperty({
    description: '菜单类型',
    enum: [0, 1, 2, 3],
    example: 0,
  })
  @IsNotEmpty()
  @IsEnum([0, 1, 2, 3], { message: '菜单类型必须是0, 1, 2或3' })
  menuType: number;

  @ApiProperty({
    description: '菜单标题',
    minLength: 1,
    maxLength: 100,
    example: '系统管理',
  })
  @IsNotEmpty()
  @IsString()
  @Length(1, 100)
  title: string;

  @ApiProperty({
    description: '菜单名称',
    required: false,
    maxLength: 100,
    example: 'system',
  })
  @IsString()
  @Length(0, 100)
  name: string;

  @ApiProperty({
    description: '菜单路径',
    required: false,
    maxLength: 200,
    example: '/system',
  })
  @IsString()
  @Length(0, 200)
  path: string;

  @ApiProperty({
    description: '组件路径',
    required: false,
    maxLength: 200,
    example: 'system/index.vue',
  })
  @IsString()
  @Length(0, 200)
  component: string;

  @ApiProperty({
    description: '排序',
    required: false,
    example: 1,
  })
  @IsOptional()
  rank: number;

  @ApiProperty({
    description: '重定向路径',
    required: false,
    maxLength: 200,
    example: '/system/user',
  })
  @IsString()
  @IsOptional()
  @Length(0, 200)
  redirect: string;

  @ApiProperty({
    description: '图标',
    required: false,
    maxLength: 100,
    example: 'el-icon-setting',
  })
  @IsString()
  @IsOptional()
  @Length(0, 100)
  icon: string;

  @ApiProperty({
    description: '额外图标',
    required: false,
    maxLength: 100,
    example: 'el-icon-arrow-right',
  })
  @IsString()
  @IsOptional()
  @Length(0, 100)
  extraIcon: string;

  @ApiProperty({
    description: '进入动画',
    required: false,
    maxLength: 100,
    example: 'fade',
  })
  @IsString()
  @IsOptional()
  @Length(0, 100)
  enterTransition: string;

  @ApiProperty({
    description: '离开动画',
    required: false,
    maxLength: 100,
    example: 'fade',
  })
  @IsString()
  @IsOptional()
  @Length(0, 100)
  leaveTransition: string;

  @ApiProperty({
    description: '激活路径',
    required: false,
    maxLength: 200,
    example: '/system/user',
  })
  @IsString()
  @IsOptional()
  @Length(0, 200)
  activePath: string;

  @ApiProperty({
    description: '权限标识',
    required: false,
    maxLength: 200,
    example: 'system:user:view',
  })
  @IsString()
  @IsOptional()
  @Length(0, 200)
  auths: string;

  @ApiProperty({
    description: 'iframe地址',
    required: false,
    maxLength: 200,
    example: 'https://example.com',
  })
  @IsString()
  @IsOptional()
  @Length(0, 200)
  frameSrc: string;

  @ApiProperty({
    description: '是否显示iframe加载动画',
    enum: [true, false],
    example: true,
  })
  @IsNotEmpty()
  @IsEnum([true, false], { message: 'frameLoading必须是true或false' })
  frameLoading: boolean;

  @ApiProperty({
    description: '是否缓存页面',
    enum: [true, false],
    example: true,
  })
  @IsNotEmpty()
  @IsEnum([true, false], { message: 'keepAlive必须是true或false' })
  keepAlive: boolean;

  @ApiProperty({
    description: '是否隐藏标签',
    enum: [true, false],
    example: false,
  })
  @IsNotEmpty()
  @IsEnum([true, false], { message: 'hiddenTag必须是true或false' })
  hiddenTag: boolean;

  @ApiProperty({
    description: '是否固定标签',
    enum: [true, false],
    example: false,
  })
  @IsNotEmpty()
  @IsEnum([true, false], { message: 'fixedTag必须是true或false' })
  fixedTag: boolean;

  @ApiProperty({
    description: '是否显示菜单',
    enum: [true, false],
    example: true,
  })
  @IsNotEmpty()
  @IsEnum([true, false], { message: 'showLink必须是true或false' })
  showLink: boolean;

  @ApiProperty({
    description: '是否显示父级菜单',
    enum: [true, false],
    example: true,
  })
  @IsNotEmpty()
  @IsEnum([true, false], { message: 'showParent必须是true或false' })
  showParent: boolean;

  @ApiProperty({
    description: '上级菜单选项',
    type: [CreateMenuDto],
    required: false,
  })
  @IsOptional()
  @Type(() => CreateMenuDto)
  @ValidateNested({ each: true })
  higherMenuOptions: CreateMenuDto[];

  @ApiProperty({
    description: '父级菜单ID',
    example: 0,
  })
  @IsNotEmpty()
  parentId: number;
}

export class SearchMenuDto {
  @IsString()
  @IsOptional()
  code: string;

  @IsString()
  @IsOptional()
  name: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsEnum([0, 1], { message: '状态必须是0或1' })
  status: 0 | 1;
}
