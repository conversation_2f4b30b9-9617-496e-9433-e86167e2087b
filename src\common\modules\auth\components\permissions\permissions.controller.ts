import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { PermissionsService } from './permissions.service';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiTags,
  ApiBody,
  ApiParam,
  ApiResponse,
} from '@nestjs/swagger';
import { LoginGuard } from 'src/common/modules/auth/guards/login.guard';

@ApiTags('permissions')
@Controller('permissions')
@ApiBearerAuth() // 开启 BearerAuth 授权认证
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  @Post()
  @ApiOperation({
    summary: '创建权限',
    description: '创建一个新的权限项',
  })
  @ApiBody({ type: CreatePermissionDto })
  @ApiResponse({
    status: 201,
    description: '权限创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  create(@Body() createPermissionDto: CreatePermissionDto) {
    return this.permissionsService.create(createPermissionDto);
  }

  @Get()
  @ApiOperation({
    summary: '查询所有权限',
    description: '获取系统中所有权限列表',
  })
  @ApiResponse({
    status: 200,
    description: '成功获取权限列表',
  })
  findAll() {
    return this.permissionsService.findAll();
  }

  @Get(':id')
  @ApiOperation({
    summary: '查询权限',
    description: '根据ID获取单个权限详情',
  })
  @ApiParam({
    name: 'id',
    description: '权限ID',
    type: Number,
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: '成功获取权限详情',
  })
  @ApiResponse({
    status: 404,
    description: '权限不存在',
  })
  findOne(@Param('id') id: string) {
    return this.permissionsService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: '更新权限',
    description: '根据ID更新权限信息',
  })
  @ApiParam({
    name: 'id',
    description: '权限ID',
    type: Number,
    example: 1,
  })
  @ApiBody({ type: UpdatePermissionDto })
  @ApiResponse({
    status: 200,
    description: '权限更新成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 404,
    description: '权限不存在',
  })
  update(
    @Param('id') id: string,
    @Body() updatePermissionDto: UpdatePermissionDto,
  ) {
    return this.permissionsService.update(+id, updatePermissionDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: '删除权限',
    description: '根据ID删除权限',
  })
  @ApiParam({
    name: 'id',
    description: '权限ID',
    type: Number,
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: '权限删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '权限不存在',
  })
  remove(@Param('id') id: string) {
    return this.permissionsService.remove(+id);
  }
}
